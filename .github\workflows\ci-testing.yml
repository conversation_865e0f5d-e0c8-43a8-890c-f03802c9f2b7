# YOLOv5 🚀 by Ultralytics, GPL-3.0 license

name: CI CPU testing

on: # https://help.github.com/en/actions/reference/events-that-trigger-workflows
  push:
    branches: [ master ]
  pull_request:
    # The branches below must be a subset of the branches above
    branches: [ master ]
  schedule:
    - cron: '0 0 * * *'  # Runs at 00:00 UTC every day

jobs:
  cpu-tests:

    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [ ubuntu-latest, macos-latest, windows-latest ]
        python-version: [ 3.8 ]
        model: [ 'yolov5s' ]  # models to test

    # Timeout: https://stackoverflow.com/a/59076067/4521646
    timeout-minutes: 50
    steps:
      - uses: actions/checkout@v2
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v2
        with:
          python-version: ${{ matrix.python-version }}

      # Note: This uses an internal pip API and may not always work
      # https://github.com/actions/cache/blob/master/examples.md#multiple-oss-in-a-workflow
      - name: Get pip cache
        id: pip-cache
        run: |
          python -c "from pip._internal.locations import USER_CACHE_DIR; print('::set-output name=dir::' + USER_CACHE_DIR)"

      - name: Cache pip
        uses: actions/cache@v1
        with:
          path: ${{ steps.pip-cache.outputs.dir }}
          key: ${{ runner.os }}-${{ matrix.python-version }}-pip-${{ hashFiles('requirements.txt') }}
          restore-keys: |
            ${{ runner.os }}-${{ matrix.python-version }}-pip-

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -qr requirements.txt -f https://download.pytorch.org/whl/cpu/torch_stable.html
          pip install -q onnx tensorflow-cpu  # for export
          python --version
          pip --version
          pip list
        shell: bash

      - name: Download data
        run: |
          # curl -L -o tmp.zip https://github.com/ultralytics/yolov5/releases/download/v1.0/coco128.zip
          # unzip -q tmp.zip -d ../
          # rm tmp.zip

      - name: Tests workflow
        run: |
          # export PYTHONPATH="$PWD"  # to run '$ python *.py' files in subdirectories
          di=cpu  # device

          # Train
          python train.py --img 64 --batch 32 --weights ${{ matrix.model }}.pt --cfg ${{ matrix.model }}.yaml --epochs 1 --device $di
          # Val
          python val.py --img 64 --batch 32 --weights ${{ matrix.model }}.pt --device $di
          python val.py --img 64 --batch 32 --weights runs/train/exp/weights/last.pt --device $di
          # Detect
          python detect.py --weights ${{ matrix.model }}.pt --device $di
          python detect.py --weights runs/train/exp/weights/last.pt --device $di
          python hubconf.py  # hub
          # Export
          python models/yolo.py --cfg ${{ matrix.model }}.yaml  # build PyTorch model
          python models/tf.py --weights ${{ matrix.model }}.pt  # build TensorFlow model
          python export.py --img 64 --batch 1 --weights ${{ matrix.model }}.pt --include torchscript onnx  # export
          # Python
          python - <<EOF
          import torch
          # Known issue, urllib.error.HTTPError: HTTP Error 403: rate limit exceeded, will be resolved in torch==1.10.0 
          # model = torch.hub.load('ultralytics/yolov5', 'custom', path='runs/train/exp/weights/last.pt')
          EOF

        shell: bash
