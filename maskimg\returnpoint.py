import cv2
import numpy as np
from maskimg.heishui.maskregion import *

# 假设pts是一个包含所有点的NumPy数组
img1 = cv2.imread('1.jpg')  # 读取图片
img = img1.transpose((2, 0, 1))
# print(img.shape)

pts = np.array([[int(img.shape[2] * wl1), int(img.shape[1] * hl1)],  # pts1
                [int(img.shape[2] * wl2), int(img.shape[1] * hl2)],  # pts2
                [int(img.shape[2] * wl3), int(img.shape[1] * hl3)],  # pts3
                [int(img.shape[2] * wl4), int(img.shape[1] * hl4)],
                [int(img.shape[2] * wl5), int(img.shape[1] * hl5)],
                [int(img.shape[2] * wl6), int(img.shape[1] * hl6)],
                [int(img.shape[2] * wl7), int(img.shape[1] * hl7)],
                [int(img.shape[2] * wl8), int(img.shape[1] * hl8)],
                [int(img.shape[2] * wl9), int(img.shape[1] * hl9)],
                [int(img.shape[2] * wl10), int(img.shape[1] * hl10)],
                [int(img.shape[2] * wl11), int(img.shape[1] * hl11)],
                [int(img.shape[2] * wl12), int(img.shape[1] * hl12)],
                [int(img.shape[2] * wl13), int(img.shape[1] * hl13)],
                [int(img.shape[2] * wl14), int(img.shape[1] * hl14)],
                [int(img.shape[2] * wl15), int(img.shape[1] * hl15)],
                [int(img.shape[2] * wl16), int(img.shape[1] * hl16)],
                [int(img.shape[2] * wl17), int(img.shape[1] * hl17)],
                [int(img.shape[2] * wl18), int(img.shape[1] * hl18)],
                [int(img.shape[2] * wl19), int(img.shape[1] * hl19)],
                [int(img.shape[2] * wl20), int(img.shape[1] * hl20)]], np.int32)


# print(pts)
# 循环遍历所有点并在图像上绘制圆形
for pt in pts:
    x, y = pt
    radius = 3  # 圆的半径为5个像素
    color = (0, 255, 0)  # 线条颜色为绿色
    thickness = -1  # 填充圆形

    cv2.circle(img1, (x, y), radius, color, thickness)

# 显示图像并等待用户按任意键退出窗口
cv2.imwrite('returnpoint.jpg', img1)
