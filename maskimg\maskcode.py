import cv2
import numpy as np
from maskimg.heishui.maskregion import *


# mask for certain region
# hl1 = 3.77 / 10
# wl1 = 2.17 / 10
# hl2 = 3.21 / 10
# wl2 = 2.64 / 10
# hl3 = 2.77 / 10
# wl3 = 3.20 / 10
# hl4 = 2.40 / 10
# wl4 = 3.79 / 10
# hl5 = 2.12 / 10
# wl5 = 4.58 / 10
# hl6 = 2.01 / 10
# wl6 = 5.31 / 10
# hl7 = 2.09 / 10
# wl7 = 6.18 / 10
# hl8 = 2.21 / 10
# wl8 = 6.72 / 10
# hl9 = 3.31 / 10
# wl9 = 7.17 / 10
# hl10 = 5.30 / 10
# wl10 = 7.52 / 10
# hl11 = 6.58 / 10
# wl11 = 7.51 / 10
# hl12 = 7.29 / 10
# wl12 = 6.87 / 10
# hl13 = 7.89 / 10
# wl13 = 6.01 / 10
# hl14 = 8.18 / 10
# wl14 = 5.27 / 10
# hl15 = 8.28 / 10
# wl15 = 4.53 / 10
# hl16 = 8.20 / 10
# wl16 = 3.91 / 10
# hl17 = 8.01 / 10
# wl17 = 3.27 / 10
# hl18 = 7.66 / 10
# wl18 = 2.41 / 10
# hl19 = 6.62 / 10
# wl19 = 2.33 / 10
# hl20 = 5.11 / 10
# wl20 = 2.12 / 10


img1 = cv2.imread('1.jpg')
img = img1.transpose((2, 0, 1))
mask = np.zeros([img.shape[1], img.shape[2]], dtype=np.uint8)
# mask[round(img.shape[1] * hl1):img.shape[1], round(img.shape[2] * wl1):img.shape[2]] = 255
# print('mask shape: ', mask.shape)
# print('np.shape: ', np.shape(img))
# print('img shape: ', img.shape)
# print('img shape[0]: ', img.shape[0])
# print('img shape[1]: ', img.shape[1])
# print('img shape[2]: ', img.shape[2])
pts = np.array([[int(img.shape[2] * wl1), int(img.shape[1] * hl1)],  # pts1
                [int(img.shape[2] * wl2), int(img.shape[1] * hl2)],  # pts2
                [int(img.shape[2] * wl3), int(img.shape[1] * hl3)],  # pts3
                [int(img.shape[2] * wl4), int(img.shape[1] * hl4)],
                [int(img.shape[2] * wl5), int(img.shape[1] * hl5)],
                [int(img.shape[2] * wl6), int(img.shape[1] * hl6)],
                [int(img.shape[2] * wl7), int(img.shape[1] * hl7)],
                [int(img.shape[2] * wl8), int(img.shape[1] * hl8)],
                [int(img.shape[2] * wl9), int(img.shape[1] * hl9)],
                [int(img.shape[2] * wl10), int(img.shape[1] * hl10)],
                [int(img.shape[2] * wl11), int(img.shape[1] * hl11)],
                [int(img.shape[2] * wl12), int(img.shape[1] * hl12)],
                [int(img.shape[2] * wl13), int(img.shape[1] * hl13)],
                [int(img.shape[2] * wl14), int(img.shape[1] * hl14)],
                [int(img.shape[2] * wl15), int(img.shape[1] * hl15)],
                [int(img.shape[2] * wl16), int(img.shape[1] * hl16)],
                [int(img.shape[2] * wl17), int(img.shape[1] * hl17)],
                [int(img.shape[2] * wl18), int(img.shape[1] * hl18)],
                [int(img.shape[2] * wl19), int(img.shape[1] * hl19)],
                [int(img.shape[2] * wl20), int(img.shape[1] * hl20)]], np.int32)
mask = cv2.fillPoly(mask, [pts], (255, 255, 255))
cv2.imwrite('mask.jpg', mask)
cv2.waitKey(0)



